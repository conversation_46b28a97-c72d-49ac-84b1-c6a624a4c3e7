@extends('layouts.admin')

@section('title', 'مدیریت درخواست‌های سایت')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">🌐 مدیریت درخواست‌های سایت</h1>
                <a href="{{ route('admin.site-requests.export', request()->query()) }}" class="btn btn-success">
                    <i class="fas fa-download"></i> خروجی Excel
                </a>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-primary">{{ number_format($stats['total_requests']) }}</h5>
                            <p class="card-text small">کل درخواست‌ها</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-warning">{{ number_format($stats['pending_requests']) }}</h5>
                            <p class="card-text small">در انتظار بررسی</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-success">{{ number_format($stats['approved_requests']) }}</h5>
                            <p class="card-text small">تایید شده</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-danger">{{ number_format($stats['rejected_requests']) }}</h5>
                            <p class="card-text small">رد شده</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.site-requests.index') }}">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label for="search" class="form-label">جستجو</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="{{ request('search') }}" placeholder="نام کاربری، نام سایت، ایمیل یا تلفن">
                            </div>
                            <div class="col-md-2">
                                <label for="status" class="form-label">وضعیت</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">همه</option>
                                    <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>در انتظار</option>
                                    <option value="approved" {{ request('status') === 'approved' ? 'selected' : '' }}>تایید شده</option>
                                    <option value="rejected" {{ request('status') === 'rejected' ? 'selected' : '' }}>رد شده</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="date_from" class="form-label">از تاریخ</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" value="{{ request('date_from') }}">
                            </div>
                            <div class="col-md-2">
                                <label for="date_to" class="form-label">تا تاریخ</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" value="{{ request('date_to') }}">
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i>
                                </button>
                                <a href="{{ route('admin.site-requests.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Bulk Actions -->
            <div class="card mb-4" id="bulk-actions" style="display: none;">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <button type="button" class="btn btn-success" onclick="showBulkApproveModal()">
                                <i class="fas fa-check"></i> تایید گروهی
                            </button>
                            <button type="button" class="btn btn-danger" onclick="showBulkRejectModal()">
                                <i class="fas fa-times"></i> رد گروهی
                            </button>
                        </div>
                        <div class="col-md-6 text-end">
                            <span id="selected-count">0</span> درخواست انتخاب شده
                        </div>
                    </div>
                </div>
            </div>

            <!-- Requests Table -->
            <div class="card">
                <div class="card-body">
                    @if($siteRequests->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>
                                            <input type="checkbox" id="select-all" onchange="toggleSelectAll()">
                                        </th>
                                        <th>شناسه</th>
                                        <th>نام کاربری</th>
                                        <th>نام سایت</th>
                                        <th>اطلاعات تماس</th>
                                        <th>وضعیت</th>
                                        <th>تاریخ ثبت</th>
                                        <th>عملیات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($siteRequests as $request)
                                        <tr>
                                            <td>
                                                @if($request->isPending())
                                                    <input type="checkbox" class="request-checkbox" value="{{ $request->id }}" onchange="updateBulkActions()">
                                                @endif
                                            </td>
                                            <td>
                                                <strong>#{{ $request->id }}</strong><br>
                                                <small class="text-muted">{{ $request->ip_address }}</small>
                                            </td>
                                            <td>
                                                <strong>{{ $request->username }}</strong>
                                            </td>
                                            <td>
                                                <strong>{{ $request->site_name }}</strong>
                                            </td>
                                            <td>
                                                @if($request->email)
                                                    <div><i class="fas fa-envelope"></i> {{ $request->email }}</div>
                                                @endif
                                                @if($request->phone)
                                                    <div><i class="fas fa-phone"></i> {{ $request->phone }}</div>
                                                @endif
                                                @if(!$request->email && !$request->phone)
                                                    <span class="text-muted">ندارد</span>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ $request->status_badge_class }}">
                                                    {{ $request->status_text }}
                                                </span>
                                                @if($request->approved_at)
                                                    <br><small class="text-muted">{{ $request->formatted_approved_at }}</small>
                                                @endif
                                            </td>
                                            <td>
                                                {{ $request->formatted_created_at }}<br>
                                                <small class="text-muted">{{ $request->created_at->diffForHumans() }}</small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.site-requests.show', $request) }}" class="btn btn-sm btn-outline-info">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    @if($request->isPending())
                                                        <button type="button" class="btn btn-sm btn-outline-success" 
                                                                onclick="quickApprove({{ $request->id }})">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                                onclick="quickReject({{ $request->id }})">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    @endif
                                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                                            onclick="deleteRequest({{ $request->id }})">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            {{ $siteRequests->appends(request()->query())->links() }}
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-globe fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">هیچ درخواستی یافت نشد</h5>
                            <p class="text-muted">درخواست‌های ثبت شده از API اینجا نمایش داده می‌شوند.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Approve Modal -->
<div class="modal fade" id="bulkApproveModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ route('admin.site-requests.bulk-approve') }}">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title">تایید گروهی درخواست‌ها</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="request_ids" id="bulk-approve-ids">
                    <div class="mb-3">
                        <label for="bulk-approve-notes" class="form-label">یادداشت (اختیاری)</label>
                        <textarea class="form-control" id="bulk-approve-notes" name="admin_notes" rows="3" 
                                  placeholder="یادداشت برای تایید گروهی..."></textarea>
                    </div>
                    <p class="text-muted">آیا از تایید <span id="approve-count">0</span> درخواست انتخاب شده اطمینان دارید؟</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
                    <button type="submit" class="btn btn-success">تایید همه</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Reject Modal -->
<div class="modal fade" id="bulkRejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ route('admin.site-requests.bulk-reject') }}">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title">رد گروهی درخواست‌ها</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="request_ids" id="bulk-reject-ids">
                    <div class="mb-3">
                        <label for="bulk-reject-notes" class="form-label">دلیل رد <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="bulk-reject-notes" name="admin_notes" rows="3" 
                                  placeholder="دلیل رد درخواست‌ها..." required></textarea>
                    </div>
                    <p class="text-muted">آیا از رد <span id="reject-count">0</span> درخواست انتخاب شده اطمینان دارید؟</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
                    <button type="submit" class="btn btn-danger">رد همه</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function toggleSelectAll() {
    const selectAll = document.getElementById('select-all');
    const checkboxes = document.querySelectorAll('.request-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateBulkActions();
}

function updateBulkActions() {
    const checkboxes = document.querySelectorAll('.request-checkbox:checked');
    const bulkActions = document.getElementById('bulk-actions');
    const selectedCount = document.getElementById('selected-count');
    
    selectedCount.textContent = checkboxes.length;
    bulkActions.style.display = checkboxes.length > 0 ? 'block' : 'none';
}

function showBulkApproveModal() {
    const checkboxes = document.querySelectorAll('.request-checkbox:checked');
    const ids = Array.from(checkboxes).map(cb => cb.value);
    
    document.getElementById('bulk-approve-ids').value = JSON.stringify(ids);
    document.getElementById('approve-count').textContent = ids.length;
    
    new bootstrap.Modal(document.getElementById('bulkApproveModal')).show();
}

function showBulkRejectModal() {
    const checkboxes = document.querySelectorAll('.request-checkbox:checked');
    const ids = Array.from(checkboxes).map(cb => cb.value);
    
    document.getElementById('bulk-reject-ids').value = JSON.stringify(ids);
    document.getElementById('reject-count').textContent = ids.length;
    
    new bootstrap.Modal(document.getElementById('bulkRejectModal')).show();
}

function quickApprove(requestId) {
    if (confirm('آیا از تایید این درخواست اطمینان دارید؟')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/site-requests/${requestId}/approve`;
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        
        form.appendChild(csrfToken);
        document.body.appendChild(form);
        form.submit();
    }
}

function quickReject(requestId) {
    const reason = prompt('دلیل رد درخواست را وارد کنید:');
    if (reason && reason.trim()) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/site-requests/${requestId}/reject`;
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        
        const notesInput = document.createElement('input');
        notesInput.type = 'hidden';
        notesInput.name = 'admin_notes';
        notesInput.value = reason.trim();
        
        form.appendChild(csrfToken);
        form.appendChild(notesInput);
        document.body.appendChild(form);
        form.submit();
    }
}

function deleteRequest(requestId) {
    if (confirm('آیا از حذف این درخواست اطمینان دارید؟ این عمل غیرقابل بازگشت است.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/site-requests/${requestId}`;
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        
        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';
        
        form.appendChild(csrfToken);
        form.appendChild(methodInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endsection
