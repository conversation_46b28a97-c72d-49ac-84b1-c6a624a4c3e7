@extends('layouts.admin')

@section('title', 'جزئیات درخواست #' . $siteRequest->id)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">جزئیات درخواست #{{ $siteRequest->id }}</h1>
                <a href="{{ route('admin.site-requests.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right"></i> بازگشت
                </a>
            </div>

            <div class="row">
                <!-- Request Details -->
                <div class="col-lg-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">اطلاعات درخواست</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>شناسه درخواست:</strong></td>
                                            <td>#{{ $siteRequest->id }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>نام کاربری:</strong></td>
                                            <td><code>{{ $siteRequest->username }}</code></td>
                                        </tr>
                                        <tr>
                                            <td><strong>نام سایت:</strong></td>
                                            <td><strong>{{ $siteRequest->site_name }}</strong></td>
                                        </tr>
                                        <tr>
                                            <td><strong>ایمیل:</strong></td>
                                            <td>{{ $siteRequest->email ?: 'ندارد' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>تلفن:</strong></td>
                                            <td>{{ $siteRequest->phone ?: 'ندارد' }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>وضعیت:</strong></td>
                                            <td>
                                                <span class="badge bg-{{ $siteRequest->status_badge_class }} fs-6">
                                                    {{ $siteRequest->status_text }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>تاریخ ثبت:</strong></td>
                                            <td>{{ $siteRequest->formatted_created_at }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>تاریخ بررسی:</strong></td>
                                            <td>{{ $siteRequest->formatted_approved_at ?: 'هنوز بررسی نشده' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>بررسی کننده:</strong></td>
                                            <td>{{ $siteRequest->approvedBy?->name ?: 'ندارد' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>IP آدرس:</strong></td>
                                            <td><code>{{ $siteRequest->ip_address ?: 'نامشخص' }}</code></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            @if($siteRequest->admin_notes)
                            <div class="mt-3">
                                <strong>یادداشت ادمین:</strong>
                                <div class="alert alert-info mt-2">
                                    {{ $siteRequest->admin_notes }}
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Technical Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">اطلاعات فنی</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless table-sm">
                                        <tr>
                                            <td><strong>User Agent:</strong></td>
                                            <td><small>{{ $siteRequest->user_agent['user_agent'] ?? 'نامشخص' }}</small></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Accept Language:</strong></td>
                                            <td><small>{{ $siteRequest->user_agent['accept_language'] ?? 'نامشخص' }}</small></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Referer:</strong></td>
                                            <td><small>{{ $siteRequest->user_agent['referer'] ?? 'ندارد' }}</small></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless table-sm">
                                        <tr>
                                            <td><strong>تاریخ ایجاد کامل:</strong></td>
                                            <td><small>{{ $siteRequest->created_at->format('Y/m/d H:i:s') }}</small></td>
                                        </tr>
                                        <tr>
                                            <td><strong>آخرین بروزرسانی:</strong></td>
                                            <td><small>{{ $siteRequest->updated_at->format('Y/m/d H:i:s') }}</small></td>
                                        </tr>
                                        <tr>
                                            <td><strong>زمان گذشته:</strong></td>
                                            <td><small>{{ $siteRequest->created_at->diffForHumans() }}</small></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Password Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">اطلاعات رمز عبور</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-warning">
                                <i class="fas fa-lock"></i> {{ $siteRequest->password }} 
                            </div>
                          
                        </div>
                    </div>
                </div>

                <!-- Actions Sidebar -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">عملیات</h5>
                        </div>
                        <div class="card-body">
                            @if($siteRequest->isPending())
                                <!-- Approve Form -->
                                <form method="POST" action="{{ route('admin.site-requests.approve', $siteRequest) }}" class="mb-3">
                                    @csrf
                                    <div class="mb-3">
                                        <label for="approve_notes" class="form-label">یادداشت تایید (اختیاری)</label>
                                        <textarea class="form-control" id="approve_notes" name="admin_notes" rows="3" 
                                                  placeholder="یادداشت برای تایید درخواست..."></textarea>
                                    </div>
                                    <button type="submit" class="btn btn-success w-100">
                                        <i class="fas fa-check"></i> تایید درخواست
                                    </button>
                                </form>

                                <!-- Reject Form -->
                                <form method="POST" action="{{ route('admin.site-requests.reject', $siteRequest) }}" class="mb-3">
                                    @csrf
                                    <div class="mb-3">
                                        <label for="reject_notes" class="form-label">دلیل رد <span class="text-danger">*</span></label>
                                        <textarea class="form-control" id="reject_notes" name="admin_notes" rows="3" 
                                                  placeholder="دلیل رد درخواست..." required></textarea>
                                    </div>
                                    <button type="submit" class="btn btn-danger w-100">
                                        <i class="fas fa-times"></i> رد درخواست
                                    </button>
                                </form>
                            @else
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i> این درخواست قبلاً بررسی شده است.
                                </div>
                            @endif

                            <!-- Delete Request -->
                            <form method="POST" action="{{ route('admin.site-requests.destroy', $siteRequest) }}" 
                                  onsubmit="return confirm('آیا از حذف این درخواست اطمینان دارید؟ این عمل غیرقابل بازگشت است.')" class="mb-3">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-outline-danger w-100">
                                    <i class="fas fa-trash"></i> حذف درخواست
                                </button>
                            </form>

                            <!-- Quick Actions -->
                            <hr>
                            <h6>دسترسی سریع</h6>
                            <a href="{{ route('admin.site-requests.index', ['search' => $siteRequest->username]) }}" class="btn btn-outline-info btn-sm w-100 mb-2">
                                <i class="fas fa-search"></i> سایر درخواست‌های این کاربر
                            </a>
                            <a href="{{ route('admin.site-requests.index', ['status' => 'pending']) }}" class="btn btn-outline-warning btn-sm w-100">
                                <i class="fas fa-clock"></i> درخواست‌های در انتظار
                            </a>
                        </div>
                    </div>

                    <!-- Request Statistics -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">آمار کلی</h6>
                        </div>
                        <div class="card-body">
                            @php
                                $userRequests = \App\Models\SiteRequest::where('username', $siteRequest->username)->get();
                                $ipRequests = \App\Models\SiteRequest::where('ip_address', $siteRequest->ip_address)->get();
                            @endphp
                            <table class="table table-borderless table-sm">
                                <tr>
                                    <td><strong>درخواست‌های این کاربر:</strong></td>
                                    <td>{{ $userRequests->count() }}</td>
                                </tr>
                                <tr>
                                    <td><strong>درخواست‌های این IP:</strong></td>
                                    <td>{{ $ipRequests->count() }}</td>
                                </tr>
                                <tr>
                                    <td><strong>تایید شده کاربر:</strong></td>
                                    <td>{{ $userRequests->where('status', 'approved')->count() }}</td>
                                </tr>
                                <tr>
                                    <td><strong>رد شده کاربر:</strong></td>
                                    <td>{{ $userRequests->where('status', 'rejected')->count() }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
