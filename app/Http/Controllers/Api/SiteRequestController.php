<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\SiteRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\RateLimiter;

class SiteRequestController extends Controller
{
    /**
     * Submit a new site request
     */
    public function store(Request $request)
    {
        // Rate limiting - 3 requests per hour per IP
        $key = 'site-request:' . $request->ip();
        if (RateLimiter::tooManyAttempts($key, 3)) {
            $seconds = RateLimiter::availableIn($key);
            return response()->json([
                'success' => false,
                'message' => 'تعداد درخواست‌های شما بیش از حد مجاز است. لطفاً ' . ceil($seconds / 60) . ' دقیقه دیگر تلاش کنید.',
                'retry_after' => $seconds
            ], 429);
        }

        // Validation
        $validator = Validator::make($request->all(), [
            'username' => 'required',
            'password' => 'required',
            'site_name' => 'required',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20|regex:/^[0-9+\-\s()]+$/',
        ], [
            'username.required' => 'نام کاربری الزامی است.',
            'username.min' => 'نام کاربری باید حداقل 3 کاراکتر باشد.',
            'username.max' => 'نام کاربری نباید بیشتر از 50 کاراکتر باشد.',
            'username.regex' => 'نام کاربری فقط می‌تواند شامل حروف انگلیسی، اعداد و _ باشد.',
            'password.required' => 'رمز عبور الزامی است.',
            'password.min' => 'رمز عبور باید حداقل 6 کاراکتر باشد.',
            'password.max' => 'رمز عبور نباید بیشتر از 100 کاراکتر باشد.',
            'site_name.required' => 'نام سایت الزامی است.',
            'site_name.min' => 'نام سایت باید حداقل 2 کاراکتر باشد.',
            'site_name.max' => 'نام سایت نباید بیشتر از 100 کاراکتر باشد.',
            'email.email' => 'فرمت ایمیل صحیح نیست.',
            'phone.regex' => 'فرمت شماره تلفن صحیح نیست.',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'اطلاعات ارسالی نامعتبر است.',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check for duplicate username
        if (SiteRequest::where('username', $request->username)->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'این نام کاربری قبلاً ثبت شده است.',
                'errors' => [
                    'username' => ['این نام کاربری قبلاً استفاده شده است.']
                ]
            ], 422);
        }

        try {
            // Create site request
            $siteRequest = SiteRequest::create([
                'username' => $request->username,
                'password' => $request->password, // Will be hashed in model boot method
                'site_name' => $request->site_name,
                'email' => $request->email,
                'phone' => $request->phone,
                'status' => SiteRequest::STATUS_PENDING,
                'ip_address' => $request->ip(),
                'user_agent' => [
                    'user_agent' => $request->userAgent(),
                    'accept_language' => $request->header('Accept-Language'),
                    'referer' => $request->header('Referer'),
                ],
            ]);

            // Hit rate limiter
            RateLimiter::hit($key, 3600); // 1 hour

            return response()->json([
                'success' => true,
                'message' => 'اکانت شما با موفقیت ثبت شد.',
                'data' => [
                    'request_id' => $siteRequest->id,
                    'username' => $siteRequest->username,
                    'site_name' => $siteRequest->site_name,
                    'status' => $siteRequest->status,
                    'status_text' => $siteRequest->status_text,
                    'created_at' => $siteRequest->formatted_created_at,
                ]
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطا در ثبت درخواست. لطفاً دوباره تلاش کنید.',
                'error' => app()->environment('local') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Check status of a site request
     */
    public function status(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'request_id' => 'required|integer|exists:site_requests,id',
            'username' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'اطلاعات ارسالی نامعتبر است.',
                'errors' => $validator->errors()
            ], 422);
        }

        $siteRequest = SiteRequest::where('id', $request->request_id)
            ->where('username', $request->username)
            ->with('approvedBy')
            ->first();

        if (!$siteRequest) {
            return response()->json([
                'success' => false,
                'message' => 'درخواست یافت نشد.',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'request_id' => $siteRequest->id,
                'username' => $siteRequest->username,
                'site_name' => $siteRequest->site_name,
                'email' => $siteRequest->email,
                'phone' => $siteRequest->phone,
                'status' => $siteRequest->status,
                'status_text' => $siteRequest->status_text,
                'admin_notes' => $siteRequest->admin_notes,
                'created_at' => $siteRequest->formatted_created_at,
                'approved_at' => $siteRequest->formatted_approved_at,
                'approved_by' => $siteRequest->approvedBy ? [
                    'name' => $siteRequest->approvedBy->name,
                    'email' => $siteRequest->approvedBy->email,
                ] : null,
            ]
        ]);
    }

    /**
     * Get all requests for a specific username (for checking history)
     */
    public function history(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'نام کاربری الزامی است.',
                'errors' => $validator->errors()
            ], 422);
        }

        $requests = SiteRequest::where('username', $request->username)
            ->with('approvedBy')
            ->orderBy('created_at', 'desc')
            ->get();

        if ($requests->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'هیچ درخواستی برای این نام کاربری یافت نشد.',
            ], 404);
        }

        $data = $requests->map(function ($siteRequest) {
            return [
                'request_id' => $siteRequest->id,
                'site_name' => $siteRequest->site_name,
                'status' => $siteRequest->status,
                'status_text' => $siteRequest->status_text,
                'admin_notes' => $siteRequest->admin_notes,
                'created_at' => $siteRequest->formatted_created_at,
                'approved_at' => $siteRequest->formatted_approved_at,
                'approved_by' => $siteRequest->approvedBy?->name,
            ];
        });

        return response()->json([
            'success' => true,
            'data' => [
                'username' => $request->username,
                'total_requests' => $requests->count(),
                'pending_requests' => $requests->where('status', SiteRequest::STATUS_PENDING)->count(),
                'approved_requests' => $requests->where('status', SiteRequest::STATUS_APPROVED)->count(),
                'rejected_requests' => $requests->where('status', SiteRequest::STATUS_REJECTED)->count(),
                'requests' => $data,
            ]
        ]);
    }
}
