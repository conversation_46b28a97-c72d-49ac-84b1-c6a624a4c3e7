<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SiteRequest;
use Illuminate\Http\Request;

class SiteRequestController extends Controller
{
    /**
     * Display a listing of site requests
     */
    public function index(Request $request)
    {
        $query = SiteRequest::with('approvedBy');

        // Search filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('username', 'like', "%{$search}%")
                  ->orWhere('site_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $siteRequests = $query->latest()->paginate(15);

        $stats = [
            'total_requests' => SiteRequest::count(),
            'pending_requests' => SiteRequest::pending()->count(),
            'approved_requests' => SiteRequest::approved()->count(),
            'rejected_requests' => SiteRequest::rejected()->count(),
        ];

        return view('admin.site-requests.index', compact('siteRequests', 'stats'));
    }

    /**
     * Display the specified site request
     */
    public function show(SiteRequest $siteRequest)
    {
        $siteRequest->load('approvedBy');
        return view('admin.site-requests.show', compact('siteRequest'));
    }

    /**
     * Approve a site request
     */
    public function approve(Request $request, SiteRequest $siteRequest)
    {
        $request->validate([
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        if (!$siteRequest->isPending()) {
            return back()->withErrors(['error' => 'فقط درخواست‌های در انتظار قابل تایید هستند.']);
        }

        $siteRequest->approve(auth()->user(), $request->admin_notes);

        return redirect()->route('admin.site-requests.show', $siteRequest)
            ->with('success', 'درخواست با موفقیت تایید شد.');
    }

    /**
     * Reject a site request
     */
    public function reject(Request $request, SiteRequest $siteRequest)
    {
        $request->validate([
            'admin_notes' => 'required|string|max:1000',
        ], [
            'admin_notes.required' => 'دلیل رد درخواست الزامی است.',
        ]);

        if (!$siteRequest->isPending()) {
            return back()->withErrors(['error' => 'فقط درخواست‌های در انتظار قابل رد هستند.']);
        }

        $siteRequest->reject(auth()->user(), $request->admin_notes);

        return redirect()->route('admin.site-requests.show', $siteRequest)
            ->with('success', 'درخواست با موفقیت رد شد.');
    }

    /**
     * Bulk approve multiple requests
     */
    public function bulkApprove(Request $request)
    {
        $request->validate([
            'request_ids' => 'required|array',
            'request_ids.*' => 'exists:site_requests,id',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        $approvedCount = 0;
        $requests = SiteRequest::whereIn('id', $request->request_ids)->pending()->get();

        foreach ($requests as $siteRequest) {
            $siteRequest->approve(auth()->user(), $request->admin_notes);
            $approvedCount++;
        }

        return back()->with('success', "{$approvedCount} درخواست با موفقیت تایید شد.");
    }

    /**
     * Bulk reject multiple requests
     */
    public function bulkReject(Request $request)
    {
        $request->validate([
            'request_ids' => 'required|array',
            'request_ids.*' => 'exists:site_requests,id',
            'admin_notes' => 'required|string|max:1000',
        ], [
            'admin_notes.required' => 'دلیل رد درخواست‌ها الزامی است.',
        ]);

        $rejectedCount = 0;
        $requests = SiteRequest::whereIn('id', $request->request_ids)->pending()->get();

        foreach ($requests as $siteRequest) {
            $siteRequest->reject(auth()->user(), $request->admin_notes);
            $rejectedCount++;
        }

        return back()->with('success', "{$rejectedCount} درخواست با موفقیت رد شد.");
    }

    /**
     * Delete a site request
     */
    public function destroy(SiteRequest $siteRequest)
    {
        $siteRequest->delete();

        return redirect()->route('admin.site-requests.index')
            ->with('success', 'درخواست با موفقیت حذف شد.');
    }

    /**
     * Export site requests to CSV
     */
    public function export(Request $request)
    {
        $query = SiteRequest::with('approvedBy');

        // Apply same filters as index
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('username', 'like', "%{$search}%")
                  ->orWhere('site_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $siteRequests = $query->latest()->get();

        $filename = 'site-requests-' . now()->format('Y-m-d-H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($siteRequests) {
            $file = fopen('php://output', 'w');
            
            // Add BOM for UTF-8
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));
            
            // CSV headers
            fputcsv($file, [
                'شناسه',
                'نام کاربری',
                'نام سایت',
                'ایمیل',
                'تلفن',
                'وضعیت',
                'یادداشت ادمین',
                'تاریخ ثبت',
                'تاریخ تایید/رد',
                'تایید کننده',
                'IP آدرس'
            ]);

            foreach ($siteRequests as $request) {
                fputcsv($file, [
                    $request->id,
                    $request->username,
                    $request->site_name,
                    $request->email ?: '-',
                    $request->phone ?: '-',
                    $request->status_text,
                    $request->admin_notes ?: '-',
                    $request->formatted_created_at,
                    $request->formatted_approved_at ?: '-',
                    $request->approvedBy?->name ?: '-',
                    $request->ip_address ?: '-'
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
