<?php

namespace App\Services;

use App\Models\TelegramBot;
use App\Models\TelegramUser;
use App\Models\Plan;
use App\Models\Agent;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class TelegramBotService
{
    private $bot;

    public function __construct(TelegramBot $bot = null)
    {
        $this->bot = $bot;
    }

    /**
     * Set the bot instance
     */
    public function setBot(TelegramBot $bot)
    {
        $this->bot = $bot;
        return $this;
    }

    /**
     * Send message to telegram user
     */
    public function sendMessage(int $chatId, string $text, array $options = [])
    {
        if (!$this->bot) {
            throw new \Exception('Bot instance not set');
        }

        $url = $this->bot->getBotApiUrl() . '/sendMessage';
        
        $data = array_merge([
            'chat_id' => $chatId,
            'text' => $text,
            'parse_mode' => 'HTML',
        ], $options);

        try {
            $response = Http::timeout(30)->post($url, $data);
            
            if ($response->successful()) {
                return $response->json();
            } else {
                Log::error('Telegram API error', [
                    'bot_id' => $this->bot->id,
                    'response' => $response->body(),
                    'status' => $response->status()
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error('Telegram send message error', [
                'bot_id' => $this->bot->id,
                'error' => $e->getMessage(),
                'chat_id' => $chatId
            ]);
            return false;
        }
    }

    /**
     * Set webhook for the bot
     */
    public function setWebhook(): bool
    {
        if (!$this->bot) {
            throw new \Exception('Bot instance not set');
        }

        $url = $this->bot->getBotApiUrl() . '/setWebhook';
        $webhookUrl = $this->bot->getWebhookUrl();

        // Skip webhook setup in local environment
        if (app()->environment('local')) {
            Log::info('Skipping webhook setup in local environment', [
                'bot_id' => $this->bot->id,
                'webhook_url' => $webhookUrl
            ]);

            $this->bot->update([
                'webhook_url' => $webhookUrl,
                'webhook_set_at' => now(),
            ]);

            return true;
        }

        try {
            $response = Http::timeout(30)->post($url, [
                'url' => $webhookUrl,
                'allowed_updates' => ['message', 'callback_query'],
            ]);

            if ($response->successful()) {
                $this->bot->update([
                    'webhook_url' => $webhookUrl,
                    'webhook_set_at' => now(),
                ]);
                
                Log::info('Webhook set successfully', [
                    'bot_id' => $this->bot->id,
                    'webhook_url' => $webhookUrl
                ]);
                
                return true;
            } else {
                Log::error('Failed to set webhook', [
                    'bot_id' => $this->bot->id,
                    'response' => $response->body()
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error('Webhook setup error', [
                'bot_id' => $this->bot->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Remove webhook
     */
    public function removeWebhook(): bool
    {
        if (!$this->bot) {
            throw new \Exception('Bot instance not set');
        }

        $url = $this->bot->getBotApiUrl() . '/deleteWebhook';

        try {
            $response = Http::timeout(30)->post($url);

            if ($response->successful()) {
                $this->bot->update([
                    'webhook_url' => null,
                    'webhook_set_at' => null,
                ]);
                return true;
            }
            return false;
        } catch (\Exception $e) {
            Log::error('Remove webhook error', [
                'bot_id' => $this->bot->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get bot info
     */
    public function getBotInfo()
    {
        if (!$this->bot) {
            throw new \Exception('Bot instance not set');
        }

        $url = $this->bot->getBotApiUrl() . '/getMe';

        try {
            $response = Http::timeout(30)->get($url);
            
            if ($response->successful()) {
                return $response->json();
            }
            return false;
        } catch (\Exception $e) {
            Log::error('Get bot info error', [
                'bot_id' => $this->bot->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Process incoming webhook update
     */
    public function processUpdate(array $update)
    {
        if (!$this->bot) {
            throw new \Exception('Bot instance not set');
        }

        Log::info('Processing telegram update', [
            'bot_id' => $this->bot->id,
            'update' => $update
        ]);

        // Handle message
        if (isset($update['message'])) {
            return $this->handleMessage($update['message']);
        }

        // Handle callback query
        if (isset($update['callback_query'])) {
            return $this->handleCallbackQuery($update['callback_query']);
        }

        return false;
    }

    /**
     * Handle incoming message
     */
    private function handleMessage(array $message)
    {
        $chatId = $message['chat']['id'];
        $text = $message['text'] ?? '';
        $from = $message['from'];

        // Get or create telegram user
        $telegramUser = $this->getOrCreateTelegramUser($from, $chatId);
        
        // Update last interaction
        $telegramUser->updateLastInteraction();

        // Handle commands
        if (str_starts_with($text, '/')) {
            return $this->handleCommand($text, $chatId, $telegramUser);
        }

        // Handle regular text
        return $this->handleText($text, $chatId, $telegramUser);
    }

    /**
     * Get or create telegram user
     */
    private function getOrCreateTelegramUser(array $from, int $chatId): TelegramUser
    {
        return TelegramUser::updateOrCreate(
            [
                'telegram_bot_id' => $this->bot->id,
                'telegram_user_id' => $from['id'],
            ],
            [
                'telegram_username' => $from['username'] ?? null,
                'first_name' => $from['first_name'] ?? null,
                'last_name' => $from['last_name'] ?? null,
                'last_interaction' => now(),
            ]
        );
    }

    /**
     * Handle bot commands
     */
    private function handleCommand(string $command, int $chatId, TelegramUser $telegramUser)
    {
        $command = strtolower(trim($command));

        switch ($command) {
            case '/start':
                return $this->handleStartCommand($chatId, $telegramUser);
            
            case '/help':
                return $this->handleHelpCommand($chatId, $telegramUser);
            
            case '/plans':
                return $this->handlePlansCommand($chatId, $telegramUser);
            
            case '/register':
                return $this->handleRegisterCommand($chatId, $telegramUser);
            
            case '/status':
                return $this->handleStatusCommand($chatId, $telegramUser);
            
            default:
                return $this->sendMessage($chatId, 
                    "دستور نامعتبر! ❌\n\nبرای مشاهده دستورات موجود /help را ارسال کنید."
                );
        }
    }

    /**
     * Handle regular text messages
     */
    private function handleText(string $text, int $chatId, TelegramUser $telegramUser)
    {
        // For now, just send a help message
        return $this->sendMessage($chatId,
            "سلام! 👋\n\nبرای استفاده از ربات، از دستورات زیر استفاده کنید:\n\n" .
            "🔹 /help - راهنما\n" .
            "🔹 /plans - مشاهده پلن‌ها\n" .
            "🔹 /register - ثبت‌نام در سایت"
        );
    }

    /**
     * Handle /start command
     */
    private function handleStartCommand(int $chatId, TelegramUser $telegramUser)
    {
        // Check if bot has mandatory channel and user is not subscribed
        if ($this->bot->hasMandatoryChannel() && !$this->isUserSubscribedToChannel($telegramUser->telegram_user_id)) {
            return $this->showChannelSubscriptionMessage($chatId, $telegramUser);
        }

        $agent = $this->bot->agent;
        $stats = $agent->getTelegramStats();

        // Custom welcome message
        $message = "🎉 سلام و خوش آمدید! 🎉\n\n";
        $message .= "🔥 به ربات اختصاصی {$agent->user->name} خوش اومدین!\n\n";
        // Create inline keyboard
        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '🌐 ورود به سایت',
                        'web_app' => ['url' => "https://miniapp.nitropardazesh.site/fa?" . $this->buildUserParams($telegramUser, $agent)]
                    ]
                ]
            ]
        ];

        return $this->sendMessage($chatId, $message, [
            'reply_markup' => json_encode($keyboard),
            'parse_mode' => 'HTML'
        ]);
    }

    /**
     * Handle /help command
     */
    private function handleHelpCommand(int $chatId, TelegramUser $telegramUser)
    {
        $agent = $this->bot->agent;

        $message = "🆘 راهنمای کامل ربات 🆘\n\n";
        $message .= "🤖 این ربات برای راحتی شما طراحی شده!\n\n";
        $message .= "� دستورات موجود:\n";
        $message .= "🏠 /start - بازگشت به منوی اصلی\n";
        $message .= "❓ /help - نمایش این راهنما\n";
        $message .= "� /plans - مشاهده پلن‌های موجود\n";
        $message .= "� /register - ثبت‌نام در سایت\n";
        $message .= "� /status - وضعیت حساب شما\n\n";
        $message .= "💡 نکات مهم:\n";
        $message .= "• برای خرید حتماً ثبت‌نام کنید\n";
        $message .= "• از کد معرف خود استفاده کنید\n";
        $message .= "• برای پشتیبانی با نماینده تماس بگیرید\n\n";
        $message .= "�‍💼 نماینده شما: <b>{$agent->user->name}</b>\n";
        $message .= "🎯 کد معرف: <code>{$agent->referral_code}</code>";

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '🏠 منوی اصلی',
                        'callback_data' => 'main_menu'
                    ],
                    [
                        'text' => '📞 تماس با نماینده',
                        'url' => "tg://user?id=" . ($agent->user->telegram_id ?? $agent->user->phone ?? '09123456789')
                    ]
                ]
            ]
        ];

        return $this->sendMessage($chatId, $message, [
            'reply_markup' => json_encode($keyboard),
            'parse_mode' => 'HTML'
        ]);
    }

    /**
     * Handle /plans command
     */
    private function handlePlansCommand(int $chatId, TelegramUser $telegramUser)
    {
        $plans = Plan::where('status', 'active')->orderBy('sort_order')->orderBy('price')->get();

        if ($plans->isEmpty()) {
            $message = "😔 متأسفانه در حال حاضر پلنی موجود نیست!\n\n";
            $message .= "🔄 لطفاً بعداً دوباره تلاش کنید یا با نماینده تماس بگیرید.";

            $keyboard = [
                'inline_keyboard' => [
                    [
                        [
                            'text' => '🏠 منوی اصلی',
                            'callback_data' => 'main_menu'
                        ],
                        [
                            'text' => '📞 تماس با نماینده',
                            'url' => "tg://user?id=" . ($this->bot->agent->user->telegram_id ?? $this->bot->agent->user->phone ?? '09123456789')
                        ]
                    ]
                ]
            ];

            return $this->sendMessage($chatId, $message, [
                'reply_markup' => json_encode($keyboard)
            ]);
        }

        $agent = $this->bot->agent;
        $message = "🎁 پلن‌های فوق‌العاده ما! 🎁\n\n";
        $message .= "� بهترین پلن‌ها رو برای شما آماده کردیم:\n\n";

        foreach ($plans as $index => $plan) {
            $emoji = ['🥉', '🥈', '🥇', '💎', '👑'][$index] ?? '⭐';
            $message .= "{$emoji} <b>{$plan->name}</b>\n";

            if ($plan->type) {
                $message .= "📂 دسته: {$plan->type}\n";
            }

            $message .= "💰 قیمت: <b>" . number_format($plan->price) . " تومان</b>\n";

            if (isset($plan->formatted_duration)) {
                $message .= "⏱ مدت: {$plan->formatted_duration}\n";
            }

            $message .= "📝 {$plan->description}\n";

            if ($plan->features) {
                $features = is_array($plan->features) ? $plan->features : json_decode($plan->features, true);
                if ($features && is_array($features)) {
                    $message .= "✨ ویژگی‌ها:\n";
                    foreach (array_slice($features, 0, 3) as $feature) {
                        $message .= "  ✅ {$feature}\n";
                    }
                    if (count($features) > 3) {
                        $message .= "  📋 و " . (count($features) - 3) . " ویژگی دیگر...\n";
                    }
                }
            }
            $message .= "\n";
        }

        $message .= "🎯 کد معرف شما: <code>{$agent->referral_code}</code>\n";
        $message .= "🎁 با این کد از تخفیف‌های ویژه بهره‌مند شوید!\n\n";
        $message .= "👇 برای خرید روی دکمه زیر کلیک کنید:";

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '🛒 خرید از فروشگاه',
                        'web_app' => ['url' => "https://miniapp.nitropardazesh.site/plans?" . $this->buildUserParams($telegramUser, $agent)]
                    ]
                ],
                [
                    [
                        'text' => '� ثبت‌نام اول',
                        'web_app' => ['url' => "https://miniapp.nitropardazesh.site/register?" . $this->buildUserParams($telegramUser, $agent)]
                    ],
                    [
                        'text' => '📞 مشاوره',
                        'url' => "tg://user?id=" . ($agent->user->telegram_id ?? $agent->user->phone ?? '09123456789')
                    ]
                ],
                [
                    [
                        'text' => '🏠 منوی اصلی',
                        'callback_data' => 'main_menu'
                    ]
                ]
            ]
        ];

        return $this->sendMessage($chatId, $message, [
            'reply_markup' => json_encode($keyboard),
            'parse_mode' => 'HTML'
        ]);
    }

    /**
     * Handle /register command
     */
    private function handleRegisterCommand(int $chatId, TelegramUser $telegramUser)
    {
        $agent = $this->bot->agent;

        if ($telegramUser->is_registered) {
            $message = "🎉 عالی! شما قبلاً عضو ما هستید! 🎉\n\n";
            $message .= "👤 نام: <b>{$telegramUser->user->name}</b>\n";
            $message .= "📧 ایمیل: <code>{$telegramUser->user->email}</code>\n";
            $message .= "✅ وضعیت: <b>فعال</b>\n\n";
            $message .= "🛒 حالا می‌تونید راحت خرید کنید!";

            $keyboard = [
                'inline_keyboard' => [
                    [
                        [
                            'text' => '🛒 خرید از فروشگاه',
                            'web_app' => ['url' => "https://miniapp.nitropardazesh.site/plans?" . $this->buildUserParams($telegramUser, $agent)]
                        ]
                    ],
                    [
                        [
                            'text' => '🌐 پنل کاربری',
                            'web_app' => ['url' => "https://miniapp.nitropardazesh.site/dashboard?" . $this->buildUserParams($telegramUser, $agent)]
                        ],
                        [
                            'text' => '📊 وضعیت من',
                            'callback_data' => 'my_status'
                        ]
                    ],
                    [
                        [
                            'text' => '🏠 منوی اصلی',
                            'callback_data' => 'main_menu'
                        ]
                    ]
                ]
            ];

            return $this->sendMessage($chatId, $message, [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'HTML'
            ]);
        } else {
            $message = "🚀 بیایید شروع کنیم! 🚀\n\n";
            $message .= "💎 با ثبت‌نام در سایت ما، دنیای جدیدی از امکانات در انتظارتونه!\n\n";
            $message .= "🎁 <b>مزایای ویژه عضویت:</b>\n";
            $message .= "✨ دسترسی به تمام پلن‌های پریمیوم\n";
            $message .= "🎯 پشتیبانی اختصاصی 24/7\n";
            $message .= "💰 تخفیف‌های ویژه و پیشنهادات منحصر به فرد\n";
            $message .= "📊 پنل کاربری پیشرفته\n";
            $message .= "🔔 اطلاع‌رسانی آخرین آپدیت‌ها\n";
            $message .= "🏆 امتیازات و جوایز ویژه\n\n";
            $message .= "� کد معرف شما: <code>{$agent->referral_code}</code>\n";
            $message .= "💡 حتماً از این کد استفاده کنید تا تخفیف بگیرید!";

            $keyboard = [
                'inline_keyboard' => [
                    [
                        [
                            'text' => '🎯 ثبت‌نام فوری',
                            'web_app' => ['url' => "https://miniapp.nitropardazesh.site/register?" . $this->buildUserParams($telegramUser, $agent)]
                        ]
                    ],
                    [
                        [
                            'text' => '� اول پلن‌ها رو ببینم',
                            'callback_data' => 'show_plans'
                        ]
                    ],
                    [
                        [
                            'text' => '📞 مشاوره با نماینده',
                            'url' => "tg://user?id=" . ($agent->user->telegram_id ?? $agent->user->phone ?? '09123456789')
                        ]
                    ],
                    [
                        [
                            'text' => '🏠 منوی اصلی',
                            'callback_data' => 'main_menu'
                        ]
                    ]
                ]
            ];

            return $this->sendMessage($chatId, $message, [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'HTML'
            ]);
        }
    }

    /**
     * Handle /status command
     */
    private function handleStatusCommand(int $chatId, TelegramUser $telegramUser)
    {
        $agent = $this->bot->agent;

        $message = "� پروفایل شما 👤\n\n";
        $message .= "� نام: <b>{$telegramUser->getFullName()}</b>\n";
        $message .= "🆔 شناسه: <code>{$telegramUser->telegram_user_id}</code>\n";

        if ($telegramUser->telegram_username) {
            $message .= "📱 یوزرنیم: @{$telegramUser->telegram_username}\n";
        }

        $message .= "📅 عضویت: " . $telegramUser->created_at->format('Y/m/d') . "\n";
        $message .= "🕐 آخرین بازدید: " . $telegramUser->last_interaction->diffForHumans() . "\n\n";

        if ($telegramUser->is_registered) {
            $message .= "✅ <b>وضعیت: ثبت‌نام شده</b>\n";
            $message .= "📧 ایمیل: {$telegramUser->user->email}\n\n";

            // Show purchase history
            $purchases = $telegramUser->user->purchases()->latest()->limit(3)->get();
            if ($purchases->count() > 0) {
                $message .= "🛒 <b>آخرین خریدها:</b>\n";
                foreach ($purchases as $purchase) {
                    $status = $purchase->status === 'completed' ? '✅' : '⏳';
                    $message .= "{$status} {$purchase->plan->name}\n";
                    $message .= "    💰 " . number_format($purchase->amount) . " تومان\n";
                }
                $message .= "\n";
            } else {
                $message .= "🛒 هنوز خریدی نداشته‌اید\n\n";
            }

            $keyboard = [
                'inline_keyboard' => [
                    [
                        [
                            'text' => '🛒 خرید جدید',
                            'callback_data' => 'show_plans'
                        ],
                        [
                            'text' => '🌐 ورود به سایت',
                            'web_app' => ['url' => "https://miniapp.nitropardazesh.site/fa?" . $this->buildUserParams($telegramUser, $agent)]
                        ]
                    ],
                    [
                        [
                            'text' => '🏠 منوی اصلی',
                            'callback_data' => 'main_menu'
                        ]
                    ]
                ]
            ];
        } else {
            $message .= "❌ <b>وضعیت: ثبت‌نام نشده</b>\n\n";
            $message .= "💡 برای استفاده کامل از خدمات، ابتدا ثبت‌نام کنید!\n\n";

            $keyboard = [
                'inline_keyboard' => [
                    [
                        [
                            'text' => '📝 ثبت‌نام کنید',
                            'callback_data' => 'register'
                        ]
                    ],
                    [
                        [
                            'text' => '🛒 مشاهده پلن‌ها',
                            'callback_data' => 'show_plans'
                        ],
                        [
                            'text' => '🏠 منوی اصلی',
                            'callback_data' => 'main_menu'
                        ]
                    ]
                ]
            ];
        }

        $message .= "👨‍� نماینده: <b>{$agent->user->name}</b>\n";
        $message .= "🎯 کد معرف: <code>{$agent->referral_code}</code>";

        return $this->sendMessage($chatId, $message, [
            'reply_markup' => json_encode($keyboard),
            'parse_mode' => 'HTML'
        ]);
    }

    /**
     * Handle callback query
     */
    private function handleCallbackQuery(array $callbackQuery)
    {
        $chatId = $callbackQuery['message']['chat']['id'];
        $messageId = $callbackQuery['message']['message_id'];
        $data = $callbackQuery['data'];
        $from = $callbackQuery['from'];

        // Get or create telegram user
        $telegramUser = $this->getOrCreateTelegramUser($from, $chatId);
        $telegramUser->updateLastInteraction();

        // Answer callback query first
        $this->answerCallbackQuery($callbackQuery['id']);

        // Handle different callback data
        switch ($data) {
            case 'main_menu':
                return $this->handleStartCommand($chatId, $telegramUser);

            case 'show_plans':
                return $this->handlePlansCommand($chatId, $telegramUser);

            case 'register':
                return $this->handleRegisterCommand($chatId, $telegramUser);

            case 'my_status':
                return $this->handleStatusCommand($chatId, $telegramUser);

            case 'help':
                return $this->handleHelpCommand($chatId, $telegramUser);

            case 'check_subscription':
                return $this->handleCheckSubscription($chatId, $telegramUser);

            default:
                return $this->sendMessage($chatId, "❌ دستور نامعتبر!");
        }
    }

    /**
     * Handle check subscription callback
     */
    private function handleCheckSubscription(int $chatId, TelegramUser $telegramUser)
    {
        if ($this->isUserSubscribedToChannel($telegramUser->telegram_user_id)) {
            // User is subscribed, show main menu
            $this->sendMessage($chatId, "✅ عضویت شما تأیید شد! خوش آمدید! 🎉");
            return $this->handleStartCommand($chatId, $telegramUser);
        } else {
            // User is not subscribed yet
            $channelTitle = $this->bot->mandatory_channel_title ?: 'کانال ما';
            return $this->sendMessage($chatId,
                "❌ هنوز عضو {$channelTitle} نشده‌اید!\n\n" .
                "لطفاً ابتدا عضو شوید، سپس دوباره تلاش کنید."
            );
        }
    }

    /**
     * Answer callback query
     */
    private function answerCallbackQuery(string $callbackQueryId, string $text = null)
    {
        $url = $this->bot->getBotApiUrl() . '/answerCallbackQuery';

        $data = ['callback_query_id' => $callbackQueryId];
        if ($text) {
            $data['text'] = $text;
        }

        try {
            Http::timeout(10)->post($url, $data);
        } catch (\Exception $e) {
            Log::error('Answer callback query error', [
                'bot_id' => $this->bot->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Check if user is subscribed to mandatory channel
     */
    private function isUserSubscribedToChannel(int $userId): bool
    {
        if (!$this->bot->hasMandatoryChannel()) {
            Log::info('No mandatory channel configured', ['user_id' => $userId]);
            return true;
        }

        $channelId = $this->bot->getChannelIdentifier();
        $url = $this->bot->getBotApiUrl() . '/getChatMember';

        Log::info('Checking channel membership', [
            'user_id' => $userId,
            'channel_id' => $channelId
        ]);

        try {
            $response = Http::timeout(10)->post($url, [
                'chat_id' => $channelId,
                'user_id' => $userId
            ]);

            Log::info('Channel membership API response', [
                'user_id' => $userId,
                'channel_id' => $channelId,
                'status_code' => $response->status(),
                'response_body' => $response->body()
            ]);

            if ($response->successful()) {
                $result = $response->json();
                if (isset($result['ok']) && $result['ok']) {
                    $status = $result['result']['status'] ?? '';
                    $isSubscribed = in_array($status, ['creator', 'administrator', 'member']);

                    Log::info('Channel membership check result', [
                        'user_id' => $userId,
                        'channel_id' => $channelId,
                        'status' => $status,
                        'is_subscribed' => $isSubscribed
                    ]);

                    return $isSubscribed;
                } else {
                    Log::warning('API response not ok', [
                        'user_id' => $userId,
                        'channel_id' => $channelId,
                        'result' => $result
                    ]);
                }
            } else {
                Log::warning('API request failed', [
                    'user_id' => $userId,
                    'channel_id' => $channelId,
                    'status_code' => $response->status(),
                    'response_body' => $response->body()
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Check channel membership error', [
                'user_id' => $userId,
                'channel_id' => $channelId,
                'error' => $e->getMessage()
            ]);
        }

        return false;
    }

    /**
     * Show channel subscription message
     */
    private function showChannelSubscriptionMessage(int $chatId, TelegramUser $telegramUser)
    {
        $agent = $this->bot->agent;
        $channelTitle = $this->bot->mandatory_channel_title ?: 'کانال ما';
        $channelUsername = $this->bot->mandatory_channel_username;

        $message = "🔒 برای استفاده از ربات، ابتدا باید عضو کانال شوید! 🔒\n\n";
        $message .= "📢 کانال: <b>{$channelTitle}</b>\n\n";
        $message .= "✅ پس از عضویت، دکمه «تأیید عضویت» را بزنید.";

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => "📢 عضویت در {$channelTitle}",
                        'url' => $channelUsername ? "https://t.me/{$channelUsername}" : "https://t.me/" . ltrim($this->bot->mandatory_channel_id, '@')
                    ]
                ],
                [
                    [
                        'text' => '✅ تأیید عضویت',
                        'callback_data' => 'check_subscription'
                    ]
                ]
            ]
        ];

        return $this->sendMessage($chatId, $message, [
            'reply_markup' => json_encode($keyboard),
            'parse_mode' => 'HTML'
        ]);
    }

    /**
     * Build URL parameters with user data for miniapp
     */
    private function buildUserParams(TelegramUser $telegramUser, Agent $agent): string
    {
        $params = [
            'agent_id' => $agent->id,
            'telegram_id' => $telegramUser->telegram_user_id,
            'first_name' => urlencode($telegramUser->first_name ?? ''),
            'last_name' => urlencode($telegramUser->last_name ?? ''),
            'username' => $telegramUser->telegram_username ?? '',
        ];

        return http_build_query(array_filter($params));
    }
}
